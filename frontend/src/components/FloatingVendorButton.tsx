import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { Building, X, ChevronRight, Handshake, ArrowRight, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface FloatingVendorButtonProps {
  className?: string;
  position?: 'right' | 'left';
  hideOnMobile?: boolean;
}

const FloatingVendorButton: React.FC<FloatingVendorButtonProps> = ({
  className = '',
  position = 'right',
  hideOnMobile = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Hide on vendor registration page or mobile if specified
  useEffect(() => {
    const shouldHide = location.pathname.includes('/vendor-registration') ||
                     (hideOnMobile && isMobile);
    setIsVisible(!shouldHide);
  }, [location.pathname, hideOnMobile, isMobile]);

  // Auto-collapse after 5 seconds when expanded
  useEffect(() => {
    if (isExpanded) {
      const timer = setTimeout(() => {
        setIsExpanded(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [isExpanded]);

  const handleVendorRegistration = () => {
    navigate('/vendor-registration');
  };

  if (!isVisible) return null;

  const positionClasses = position === 'right'
    ? 'right-4'
    : 'left-4';

  const mobileClasses = isMobile
    ? 'bottom-20 top-auto translate-y-0'
    : 'top-1/2 -translate-y-1/2';

  return (
    <div className={`fixed ${positionClasses} ${mobileClasses} z-50 ${className}`}>
      <AnimatePresence mode="wait">
        {!isExpanded ? (
          // Collapsed floating button
          <motion.div
            key="collapsed"
            initial={{ x: position === 'right' ? 100 : -100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: position === 'right' ? 100 : -100, opacity: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="relative"
          >
            {/* Floating animation wrapper */}
            <motion.div
              animate={{
                y: [-5, 5, -5],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                repeatType: "loop",
                ease: "easeInOut",
              }}
            >
              <Button
                onClick={() => setIsExpanded(true)}
                className="group relative h-14 w-14 rounded-full bg-gradient-to-r from-rashmi-red to-red-700 hover:from-red-700 hover:to-rashmi-red shadow-lg hover:shadow-xl transition-all duration-300 border-2 border-white/20 backdrop-blur-sm"
                size="icon"
              >
                {/* Pulse animation */}
                <motion.div
                  className="absolute inset-0 rounded-full bg-rashmi-red/30"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.7, 0, 0.7],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "loop",
                  }}
                />
                
                <Building className="h-6 w-6 text-white group-hover:scale-110 transition-transform duration-200" />
                
                {/* Tooltip indicator */}
                <motion.div
                  className="absolute -left-2 top-1/2 -translate-y-1/2 w-2 h-2 bg-green-500 rounded-full"
                  animate={{
                    scale: [1, 1.3, 1],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    repeatType: "loop",
                  }}
                />
              </Button>
            </motion.div>

            {/* Hover tooltip */}
            <motion.div
              className={`absolute ${position === 'right' ? 'right-16' : 'left-16'} top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 pointer-events-none`}
              initial={{ x: position === 'right' ? 10 : -10, opacity: 0 }}
              whileHover={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.2 }}
            >
              <div className="bg-black/80 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap backdrop-blur-sm">
                Become a Vendor
                <div className={`absolute ${position === 'right' ? 'left-full' : 'right-full'} top-1/2 -translate-y-1/2 border-4 border-transparent ${position === 'right' ? 'border-l-black/80' : 'border-r-black/80'}`}></div>
              </div>
            </motion.div>
          </motion.div>
        ) : (
          // Expanded slider panel
          <motion.div
            key="expanded"
            initial={{ x: position === 'right' ? 100 : -100, opacity: 0, scale: 0.9 }}
            animate={{ x: 0, opacity: 1, scale: 1 }}
            exit={{ x: position === 'right' ? 100 : -100, opacity: 0, scale: 0.9 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            <Card className={`${isMobile ? 'w-72' : 'w-80'} shadow-2xl border-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md`}>
              <CardContent className="p-6">
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-r from-rashmi-red to-red-700 rounded-full">
                      <Handshake className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg text-foreground">Partner With Us</h3>
                      <p className="text-sm text-muted-foreground">Join our vendor network</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsExpanded(false)}
                    className="h-8 w-8 hover:bg-muted/50"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Content */}
                <div className="space-y-4">
                  <div className="text-sm text-muted-foreground">
                    <p className="mb-3">
                      Join <strong className="text-rashmi-red">Rashmi Metaliks</strong> as a trusted vendor and be part of the world's 2nd largest ductile iron pipe manufacturer's supply chain.
                    </p>
                    
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-xs">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Quick registration process</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Global business opportunities</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span>Reliable partnership terms</span>
                      </div>
                    </div>
                  </div>

                  {/* CTA Buttons */}
                  <div className="space-y-2 pt-2">
                    <Button
                      onClick={handleVendorRegistration}
                      className="w-full bg-gradient-to-r from-rashmi-red to-red-700 hover:from-red-700 hover:to-rashmi-red text-white font-semibold group"
                    >
                      <Building className="h-4 w-4 mr-2" />
                      Register as Vendor
                      <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      onClick={() => setIsExpanded(false)}
                      className="w-full text-sm border-muted-foreground/20 hover:bg-muted/50"
                    >
                      Maybe Later
                    </Button>
                  </div>

                  {/* Stats */}
                  <div className="flex justify-between pt-3 border-t border-muted/30">
                    <div className="text-center">
                      <div className="text-lg font-bold text-rashmi-red">500+</div>
                      <div className="text-xs text-muted-foreground">Active Vendors</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-rashmi-red">50+</div>
                      <div className="text-xs text-muted-foreground">Countries</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-rashmi-red">40+</div>
                      <div className="text-xs text-muted-foreground">Years</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default FloatingVendorButton;
